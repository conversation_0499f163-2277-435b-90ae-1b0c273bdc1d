import type { Metadata } from "next";
// 临时注释掉Google字体导入，以便构建成功
// import { Inter, Roboto_Mono } from "next/font/google";
import "./globals.css";
import MobileScrollFix from "./components/MobileScrollFix";
import Providers from "./components/Providers";

// 临时注释掉字体配置
// const inter = Inter({
//   variable: "--font-inter",
//   subsets: ["latin"],
// });

// const robotoMono = Roboto_Mono({
//   variable: "--font-roboto-mono",
//   subsets: ["latin"],
// });



export const metadata: Metadata = {
  title: "Game Payment Portal",
  description: "A portal for game payments",
};

// 客户端组件包装器
import ClientLayout from "./components/ClientLayout";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        {/* 客服SDK - 移除async确保同步加载 */}
        <script
          type="text/javascript"
          src="https://kfapi.quickapi.net/static/app/libQuickService.js"
        />
      </head>
      <body
        className="antialiased"
        suppressHydrationWarning
      >
        <Providers>
          {children}
        </Providers>
      </body>
    </html>
  );
}
