// 服务器启动脚本
import { exec } from 'child_process';
const PORT = process.env.PORT || 3000;

console.log(`Starting server on port ${PORT}...`);

// 执行npm start命令并传递PORT环境变量
const child = exec(`set PORT=${PORT} && npm start`, (error, stdout, stderr) => {
  if (error) {
    console.error(`执行错误: ${error}`);
    return;
  }
  console.log(`标准输出: ${stdout}`);
  if (stderr) {
    console.error(`标准错误: ${stderr}`);
  }
});

// 将子进程的输出流连接到当前进程的输出流
child.stdout.pipe(process.stdout);
child.stderr.pipe(process.stderr);

// 优雅处理进程退出
process.on('SIGINT', () => {
  console.log('正在关闭服务器...');
  child.kill('SIGINT');
  process.exit();
}); 