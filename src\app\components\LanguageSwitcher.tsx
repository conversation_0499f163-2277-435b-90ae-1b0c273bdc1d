"use client";

import { useState, useRef, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useLocale } from "next-intl";

// 支持的语言列表
const locales = ['zh', 'en', 'ko'] as const;

const languageNames: Record<string, string> = {
  zh: "中文",
  en: "English",
  ko: "한국어",
};

// 使用更精美的图标 - 简洁的地球图标
const LanguageIcon = () => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    className="h-5 w-5" 
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    strokeWidth="2" 
    strokeLinecap="round" 
    strokeLinejoin="round"
  >
    <circle cx="12" cy="12" r="10"></circle>
    <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
    <line x1="2" y1="12" x2="22" y2="12"></line>
  </svg>
);

export default function LanguageSwitcher() {
  const pathname = usePathname();
  const router = useRouter();
  const currentLocale = useLocale();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // 获取当前路径，去除语言前缀
  const pathnameWithoutLocale = pathname.replace(`/${currentLocale}`, "");
  
  const handleLocaleChange = (newLocale: string) => {
    // 构建新的URL路径，添加新的语言前缀
    const newPath = `/${newLocale}${pathnameWithoutLocale || ""}`;
    router.push(newPath);
    setIsOpen(false);
  };
  
  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center justify-center p-2 rounded-full bg-gradient-to-r from-indigo-500/20 to-purple-500/20 hover:from-indigo-500/30 hover:to-purple-500/30 text-indigo-300 transition-all duration-300 transform hover:scale-105"
        aria-label="切换语言"
      >
        <LanguageIcon />
      </button>
      
      {isOpen && (
        <div className="absolute right-0 mt-2 w-40 rounded-lg shadow-lg bg-zinc-800/90 backdrop-blur-lg ring-1 ring-zinc-700/50 py-1 animate-fadeIn overflow-hidden">
          {locales.map((locale) => (
            <button
              key={locale}
              onClick={() => handleLocaleChange(locale)}
              className={`w-full text-left px-4 py-3 text-sm flex items-center space-x-3 transition-colors ${
                currentLocale === locale
                  ? "bg-gradient-to-r from-indigo-600/20 to-purple-600/20 text-white"
                  : "text-zinc-300 hover:bg-zinc-700/50"
              }`}
            >
              <span className="font-medium">{languageNames[locale]}</span>
              {currentLocale === locale && (
                <svg className="ml-auto h-4 w-4 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
} 