{"games": {"pubg-mobile": {"title": "PUBG Mobile", "description": "PUBG Mobile is a multiplayer online battle royale game developed by Tencent Games, where players fight against each other on a continuously shrinking map to be the last one standing.", "category": "Shooter", "products": {"pubg-uc-60": {"title": "60 UC", "description": "Can be used to purchase in-game items and outfits"}, "pubg-uc-300": {"title": "300 UC", "description": "Can be used to purchase in-game items and outfits"}, "pubg-uc-600": {"title": "600 UC", "description": "Can be used to purchase in-game items and outfits"}, "pubg-uc-1500": {"title": "1500 UC", "description": "Can be used to purchase in-game items and outfits"}, "pubg-uc-3000": {"title": "3000 UC", "description": "Can be used to purchase in-game items and outfits"}, "pubg-royale-pass": {"title": "Elite Royale Pass", "description": "Unlock additional in-game rewards and challenges"}}}, "honor-of-kings": {"title": "Honor of Kings", "description": "Honor of Kings is a multiplayer online battle arena game developed by Tencent Games, where players select different hero characters and work with teammates to defeat the opposing team.", "category": "MOBA", "products": {"hok-diamond-60": {"title": "60 Diamonds", "description": "Can be used to purchase in-game heroes and skins"}, "hok-diamond-300": {"title": "300 Diamonds", "description": "Can be used to purchase in-game heroes and skins"}, "hok-diamond-600": {"title": "600 Diamonds", "description": "Can be used to purchase in-game heroes and skins"}, "hok-diamond-1500": {"title": "1500 Diamonds", "description": "Can be used to purchase in-game heroes and skins"}, "hok-diamond-3000": {"title": "3000 Diamonds", "description": "Can be used to purchase in-game heroes and skins"}, "hok-battle-pass": {"title": "Glory Pass", "description": "Unlock additional in-game rewards and challenges"}}}}, "ui": {"backToHome": "Back to Home", "gameNotFound": "Game Not Found", "gameNotFoundDesc": "The game you requested could not be found.", "gameIntro": "Game Introduction", "selectRechargeItem": "Select Recharge Item", "selected": "Selected", "pleaseSelect": "Please select a recharge item", "payNow": "Pay Now", "login": "<PERSON><PERSON>"}, "login": {"title": "Game Login", "username": "Username", "password": "Password", "usernamePlaceholder": "Enter your username", "passwordPlaceholder": "Enter your password", "loginButton": "<PERSON><PERSON>", "loggingIn": "Logging in...", "close": "Close", "usernameRequired": "Please enter your username", "passwordRequired": "Please enter your password", "invalidCredentials": "<PERSON><PERSON> failed, please check your username and password", "serverError": "<PERSON><PERSON> failed, server response error", "networkError": "<PERSON><PERSON> failed, please check your network connection", "welcome": "Welcome back", "logout": "Logout"}}