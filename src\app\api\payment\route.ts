import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    const params = new URLSearchParams();

    // 正确处理 FormData 到 URLSearchParams 的转换
    formData.forEach((value, key) => {
      params.append(key, value.toString());
    });

    // 将参数作为Query参数添加到URL中
    const apiUrl = `https://sdkapi.manmanyouhudong.com/msite/dopay?${params.toString()}`;

    console.log('支付请求URL:', apiUrl);
    console.log('支付请求参数:', Object.fromEntries(params));

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': '*/*',
        'Host': 'sdkapi.manmanyouhudong.com',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-store, no-cache, must-revalidate, post-check=0',
      },
    });

    console.log('支付API响应状态:', response.status);
    console.log('支付API响应头:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('支付API响应数据:', data);

    return NextResponse.json(data);
  } catch (error) {
    console.error('Payment proxy error:', error);
    return NextResponse.json(
      { status: false, message: '支付请求失败' },
      { status: 500 }
    );
  }
}