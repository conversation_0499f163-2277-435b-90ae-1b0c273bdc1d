import { useTranslations, useLocale } from 'next-intl';
import { GAMES_DATA, Game as OldGame, Product as OldProduct } from './data';
import { ApiGame, ApiProduct, SupportedLocale, Game, Product, NewApiGame, NewApiProduct } from './types';
import { GameListItem, GameI18nItem } from './api';

export interface GameWithTranslations extends OldGame {
  title: string;
  description: string;
  category: string;
  products: (OldProduct & {
    title: string;
    description: string;
  })[];
}

/**
 * 获取带有国际化翻译的游戏数据
 * @param gameId 游戏ID
 * @returns 包含翻译的游戏数据
 */
export function useGameWithTranslations(gameId: string): GameWithTranslations | null {
  const game = GAMES_DATA[gameId];
  const t = useTranslations();
  
  if (!game) return null;
  
  // 获取游戏的国际化文本
  const gameWithTranslations: GameWithTranslations = {
    ...game,
    title: t(`games.${gameId}.title`),
    description: t(`games.${gameId}.description`),
    category: t(`games.${gameId}.category`),
    products: game.products.map(product => ({
      ...product,
      title: t(`games.${gameId}.products.${product.id}.title`),
      description: t(`games.${gameId}.products.${product.id}.description`),
    })),
  };
  
  return gameWithTranslations;
}

/**
 * 获取所有带有国际化翻译的游戏数据
 * @returns 包含翻译的游戏数据列表
 */
export function useAllGamesWithTranslations(): Record<string, GameWithTranslations> {
  const t = useTranslations();
  const result: Record<string, GameWithTranslations> = {};
  
  Object.keys(GAMES_DATA).forEach(gameId => {
    const game = GAMES_DATA[gameId];
    result[gameId] = {
      ...game,
      title: t(`games.${gameId}.title`),
      description: t(`games.${gameId}.description`),
      category: t(`games.${gameId}.category`),
      products: game.products.map(product => ({
        ...product,
        title: t(`games.${gameId}.products.${product.id}.title`),
        description: t(`games.${gameId}.products.${product.id}.description`),
      })),
    };
  });
  
  return result;
}

/**
 * 从API游戏数据中提取当前语言的游戏数据，确保所有字段都不为 undefined
 * @param apiGame API返回的游戏数据（可能包含旧格式i18n/i18nObj或新格式i18ns）
 * @param locale 当前语言
 * @returns 当前语言的游戏数据
 */
export function extractGameData(apiGame: ApiGame | NewApiGame | GameListItem, locale: SupportedLocale): Game | null {
  if (!apiGame) return null;

  // 解析 i18nObj 或尝试将 i18n JSON 字符串转换为对象
  let i18nData = null;
  if ('i18nObj' in apiGame && apiGame.i18nObj) {
    i18nData = apiGame.i18nObj;
  } else if ('i18n' in apiGame) {
    // 尝试解析字符串
    if (typeof apiGame.i18n === 'string') {
      try {
        i18nData = JSON.parse(apiGame.i18n);
      } catch (e) {
        console.error('无法解析 i18n 字符串:', e);
      }
    } else {
      i18nData = apiGame.i18n;
    }
  }

  // 准备产品数组
  let productsData = [];
  // 先检查 productsArray
  if ('productsArray' in apiGame && Array.isArray(apiGame.productsArray)) {
    productsData = apiGame.productsArray;
  // 再检查 productList
  } else if ('productList' in apiGame && Array.isArray(apiGame.productList)) {
    productsData = apiGame.productList;
  // 最后检查 products ，可能是字符串或数组
  } else if ('products' in apiGame) {
    if (typeof apiGame.products === 'string') {
      try {
        productsData = JSON.parse(apiGame.products);
      } catch (e) {
        console.error('无法解析 products 字符串:', e);
      }
    } else if (Array.isArray(apiGame.products)) {
      productsData = apiGame.products;
    }
  }

  // 检查是否为新格式 (GameListItem 带有 i18ns)
  if ('i18ns' in apiGame && Array.isArray(apiGame.i18ns) && apiGame.i18ns.length > 0) {
    // 处理新的 i18ns 数组格式
    const i18nItem = apiGame.i18ns.find((item: GameI18nItem) => item.locale === locale);
    const fallbackI18n = apiGame.i18ns.find((item: GameI18nItem) => item.locale === 'en') || apiGame.i18ns[0];
    
    return {
      id: apiGame.id.toString(),
      bannerUrl: apiGame.bannerUrl || '',
      imageUrl: 'iconUrl' in apiGame ? apiGame.iconUrl || '' : (apiGame.imageUrl || ''),
      rating: typeof apiGame.rating === 'number' ? apiGame.rating : 0,
      playerCount: apiGame.playerCount || '0',
      currency: 'currency' in apiGame ? apiGame.currency : undefined,
      title: i18nItem?.title || fallbackI18n?.title || ('name' in apiGame ? apiGame.name : '') || '',
      description: i18nItem?.description || fallbackI18n?.description || '',
      category: i18nItem?.category || fallbackI18n?.category || '',
      products: productsData.map((product: ApiProduct | NewApiProduct) => extractProductData(product, locale)),
    };
  } else if (i18nData) {
    // 处理一般的 i18n 或 i18nObj 对象格式
    return {
      id: apiGame.id.toString(),
      bannerUrl: apiGame.bannerUrl || '',
      imageUrl: 'iconUrl' in apiGame ? apiGame.iconUrl || '' : (apiGame.imageUrl || ''),
      rating: typeof apiGame.rating === 'number' ? apiGame.rating : 0,
      playerCount: apiGame.playerCount || '0',
      currency: 'currency' in apiGame ? apiGame.currency : undefined,
      title: i18nData.title?.[locale] || i18nData.title?.['en'] || '',
      description: i18nData.description?.[locale] || i18nData.description?.['en'] || '',
      category: i18nData.category?.[locale] || i18nData.category?.['en'] || '',
      products: productsData.map((product: ApiProduct | NewApiProduct) => extractProductData(product, locale)),
    };
  } else {
    // 处理无国际化数据的情况
    return {
      id: apiGame.id.toString(),
      bannerUrl: apiGame.bannerUrl || '',
      imageUrl: 'iconUrl' in apiGame ? apiGame.iconUrl || '' : (apiGame.imageUrl || ''),
      rating: typeof apiGame.rating === 'number' ? apiGame.rating : 0,
      playerCount: apiGame.playerCount || '0',
      currency: 'currency' in apiGame ? apiGame.currency : undefined,
      title: 'name' in apiGame ? apiGame.name || '' : '',
      description: '',
      category: '',
      products: productsData.map((product: ApiProduct | NewApiProduct) => extractProductData(product, locale)),
    };
  }
}

/**
 * 从API产品数据中提取当前语言的产品数据，确保所有字段都不为 undefined
 * @param apiProduct API返回的产品数据（包含多语言）
 * @param locale 当前语言
 * @returns 当前语言的产品数据
 */
export function extractProductData(apiProduct: ApiProduct | NewApiProduct, locale: SupportedLocale): Product {
  // 解析 i18n 数据
  let i18nData = null;
  
  // 检查是否为新的 i18nObj 格式
  if ('i18nObj' in apiProduct && apiProduct.i18nObj) {
    i18nData = apiProduct.i18nObj;
  } else if ('i18n' in apiProduct) {
    // 尝试解析字符串
    if (typeof apiProduct.i18n === 'string') {
      try {
        i18nData = JSON.parse(apiProduct.i18n);
      } catch (e) {
        console.error('无法解析产品 i18n 字符串:', e);
      }
    } else {
      i18nData = apiProduct.i18n;
    }
  }
  
  // 如果有国际化数据
  if (i18nData) {
    return {
      id: typeof apiProduct.id === 'string' ? apiProduct.id : String(apiProduct.id),
      imageUrl: typeof apiProduct.imageUrl === 'string' ? apiProduct.imageUrl : '',
      price: typeof apiProduct.price === 'number' ? apiProduct.price : 0,
      originalPrice: typeof apiProduct.originalPrice === 'number' ? apiProduct.originalPrice : undefined,
      currency: 'currency' in apiProduct ? apiProduct.currency : undefined,
      skuCode: 'skuCode' in apiProduct && typeof apiProduct.skuCode === 'string' ? apiProduct.skuCode : undefined,
      title: i18nData.title?.[locale] || i18nData.title?.['en'] || '',
      description: i18nData.description?.[locale] || i18nData.description?.['en'] || '',
    };
  }
  
  // 处理无国际化数据的情况
  return {
    id: typeof apiProduct.id === 'string' ? apiProduct.id : String(apiProduct.id),
    imageUrl: typeof apiProduct.imageUrl === 'string' ? apiProduct.imageUrl : '',
    price: typeof apiProduct.price === 'number' ? apiProduct.price : 0,
    originalPrice: typeof apiProduct.originalPrice === 'number' ? apiProduct.originalPrice : undefined,
    currency: 'currency' in apiProduct ? apiProduct.currency : undefined,
    skuCode: 'skuCode' in apiProduct && typeof apiProduct.skuCode === 'string' ? apiProduct.skuCode : undefined,
    title: 'title' in apiProduct && typeof apiProduct.title === 'string' ? apiProduct.title :
           'name' in apiProduct && typeof apiProduct.name === 'string' ? apiProduct.name : '',
    description: 'description' in apiProduct && typeof apiProduct.description === 'string' ? apiProduct.description : '',
  };
}

/**
 * React Hook: 从API游戏数据中提取当前语言的游戏数据
 * @param apiGame API返回的游戏数据（包含多语言）
 * @returns 当前语言的游戏数据
 */
export function useExtractGameData(apiGame: ApiGame | NewApiGame | GameListItem | null): Game | null {
  const locale = useLocale() as SupportedLocale;
  
  if (!apiGame) return null;
  
  return extractGameData(apiGame, locale);
}

/**
 * React Hook: 从API游戏数据列表中提取当前语言的游戏数据列表
 * @param apiGames API返回的游戏数据列表（包含多语言）
 * @returns 当前语言的游戏数据列表
 */
export function useExtractGamesData(apiGames: (ApiGame | NewApiGame | GameListItem)[]): Game[] {
  const locale = useLocale() as SupportedLocale;
  
  if (!apiGames || apiGames.length === 0) return [];
  
  return apiGames.map(game => {
    const extracted = extractGameData(game, locale);
    return extracted || {
      id: typeof game.id === 'number' ? game.id.toString() : game.id,
      bannerUrl: game.bannerUrl || '',
      imageUrl: 'iconUrl' in game ? game.iconUrl || '' : (game.imageUrl || ''),
      rating: typeof game.rating === 'number' ? game.rating : 0,
      playerCount: game.playerCount || '0',
      currency: 'currency' in game && typeof game.currency === 'string' ? game.currency : undefined,
      title: 'name' in game ? game.name || '' : '',
      description: '',
      category: '',
      products: []
    };
  });
}

/**
 * React Hook: 从游戏列表项数据中提取当前语言的游戏数据列表
 * @param gameItems API返回的游戏列表项数据
 * @returns 当前语言的游戏数据列表
 */
export function useExtractGameListData(gameItems: GameListItem[]): Game[] {
  const locale = useLocale() as SupportedLocale;
  
  if (!gameItems || gameItems.length === 0) return [];
  
  // 确保稳定排序，避免在客户端和服务器端有不同顺序
  const sortedItems = [...gameItems].sort((a, b) => a.id - b.id);
  
  // 使用统一的 extractGameData 函数
  return sortedItems.map(gameItem => extractGameData(gameItem, locale) || {
    id: gameItem.id.toString(),
    bannerUrl: gameItem.bannerUrl || '',
    imageUrl: gameItem.iconUrl || '',
    rating: typeof gameItem.rating === 'number' ? gameItem.rating : 0,
    playerCount: gameItem.playerCount || '0',
    currency: 'currency' in gameItem && typeof gameItem.currency === 'string' ? gameItem.currency : undefined,
    title: gameItem.name || '',
    description: '',
    category: '',
    products: []
  });
}

/**
 * u6839u636eu8d27u5e01u4ee3u7801u8fd4u56deu76f8u5e94u7684u8d27u5e01u7b26u53f7
 * @param currency u8d27u5e01u4ee3u7801
 * @returns u8d27u5e01u7b26u53f7
 */
export function getCurrencySymbol(currency?: string): string {
  if (!currency) return '¥'; // 默认使用人民币符号
  
  // 转为大写并去除空格处理
  const currencyCode = currency.toUpperCase().trim();
  
  switch (currencyCode) {
    case 'CNY':
      return '¥'; // 人民币
    case 'USD':
      return '$'; // 美元
    case 'EUR':
      return '€'; // 欧元
    case 'GBP':
      return '£'; // 英镑
    case 'JPY':
      return '¥'; // 日元
    case 'KRW':
      return '₩'; // 韩元
    case 'HKD':
      return 'HK$'; // 港币
    case 'TWD':
      return 'NT$'; // 新台币
    default:
      return currency + ' '; // 其他货币直接显示代码加空格
  }
} 