"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import { useTranslations, useLocale } from "next-intl";
import { fetchGamesData } from "../../lib/games/api";
import { useExtractGamesData } from "../../lib/games/utils";
import Layout from "../../components/Layout";
import { GameListItem } from "../../lib/games/api";

export default function GamesPage() {
  const t = useTranslations();
  const locale = useLocale() as string;
  
  // 状态管理
  const [apiGamesData, setApiGamesData] = useState<GameListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 从API获取游戏数据
  useEffect(() => {
    async function loadGamesData() {
      setLoading(true);
      try {
        const data = await fetchGamesData();
        setApiGamesData(data);
      } catch (err) {
        setError("加载游戏列表失败");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }
    
    loadGamesData();
  }, []);
  
  // 提取本地化的游戏数据
  const games = useExtractGamesData(apiGamesData);
  
  // 加载状态
  if (loading) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <div className="w-16 h-16 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-zinc-400">{t('common.loading')}</p>
        </div>
      </Layout>
    );
  }
  
  // 错误状态
  if (error) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <h1 className="text-2xl font-bold text-white mb-4">{t('common.error')}</h1>
          <p className="text-zinc-400 mb-8">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            {t('common.retry')}
          </button>
        </div>
      </Layout>
    );
  }
  
  // 没有游戏状态
  if (games.length === 0) {
    return (
      <Layout>
        <div className="flex flex-col items-center justify-center py-20">
          <h1 className="text-2xl font-bold text-white mb-4">{t('ui.noGamesFound')}</h1>
          <p className="text-zinc-400 mb-8">{t('ui.noGamesFound')}</p>
          <Link
            href={`/${locale}`}
            className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors"
          >
            {t('ui.backToHome')}
          </Link>
        </div>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <div className="mb-12">
        <h1 className="text-3xl md:text-4xl font-bold text-white mb-6">
          {t('game.list.title')}
        </h1>
        <p className="text-zinc-400 max-w-3xl">
          {t('game.list.subtitle')}
        </p>
      </div>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {games.map((game) => (
          <Link 
            href={`/${locale}/games/${game.id}`}
            key={game.id}
            className="group block bg-zinc-800/50 hover:bg-zinc-800/70 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg hover:shadow-purple-500/10 hover:-translate-y-1"
          >
            <div className="relative h-48 overflow-hidden">
              <Image
                src={game.bannerUrl || game.imageUrl || "/images/games/1.png"}
                alt={game.title}
                fill
                className="object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
              
              <div className="absolute bottom-0 left-0 right-0 p-4">
                <h2 className="text-xl font-bold text-white group-hover:text-indigo-300 transition-colors">
                  {game.title}
                </h2>
                <div className="flex items-center space-x-3 mt-2">
                  <span className="text-xs font-semibold px-2 py-1 rounded-full bg-indigo-500/20 text-indigo-300">
                    {game.category || t('nav.games')}
                  </span>
                  <span className="text-xs text-zinc-300 flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-3 w-3 mr-1 text-yellow-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                      strokeWidth={2}
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"
                      />
                    </svg>
                    {game.rating}
                  </span>
                </div>
              </div>
            </div>
            
            <div className="p-4">
              <p className="text-zinc-400 text-sm line-clamp-2 h-10">
                {game.description || t('game.notFound')}
              </p>
              <div className="mt-4 flex justify-end">
                <div className="px-3 py-1 rounded-md bg-gradient-to-r from-indigo-600/20 to-purple-600/20 text-indigo-300 text-xs font-medium">
                  {t('game.list.view')}
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </Layout>
  );
} 