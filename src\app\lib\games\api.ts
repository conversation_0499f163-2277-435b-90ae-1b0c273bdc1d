import { ApiGame } from "./types";
import { API_BASE_URL } from "@/app/config/api";

interface SiteProduct {
  skuCode: string;
  skuName: string;
  price: number;
  currency: string;
  description?: string;
  imageUrl?: string;
}

interface SiteInfoResponse {
  serverUrl: string;
  productCode: string;
  productId: string | number;
  products: SiteProduct[];
}

interface PaymentParams {
  productCode: string;
  goodsId: string;
  subject: string;
  roleId: string;
  serverId: string;
  uid: string;
  ulang?: string;
}

interface PaymentResponse {
  status: boolean;
  data: {
    payUrl: string;
  };
  message?: string;
}

interface ApiResponse<T> {
  code: number;
  data: T;
  msg?: string;
}

export async function fetchSiteInfo(gameId: string): Promise<SiteInfoResponse> {
  const response = await fetch(`${API_BASE_URL}/site/paySite/getSiteInfo?id=${gameId}`);
  if (!response.ok) {
    throw new Error('获取站点信息失败');
  }
  const data = await response.json() as ApiResponse<SiteInfoResponse>;
  if (data.code !== 0) {
    throw new Error(data.msg || '获取站点信息失败');
  }
  return data.data;
}

export async function fetchGameData(gameId: string): Promise<ApiGame> {
  const response = await fetch(`${API_BASE_URL}/site/paySite/getGamePayList`);
  if (!response.ok) {
    throw new Error('获取游戏数据失败');
  }
  
  const data = await response.json() as ApiResponse<ApiGame>;
  if (data.code !== 0) {
    throw new Error(data.msg || '获取游戏数据失败');
  }
  return data.data;
}

export async function doPayment(params: PaymentParams): Promise<PaymentResponse> {
  const response = await fetch(`https://sdkapi.manmanyouhudong.com/msite/dopay`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params),
  });
  if (!response.ok) {
    throw new Error('支付请求失败');
  }
  const data = await response.json() as ApiResponse<PaymentResponse>;
  if (data.code !== 0) {
    throw new Error(data.msg || '支付请求失败');
  }
  return data.data;
}

export type { SiteProduct, SiteInfoResponse, PaymentParams, PaymentResponse }; 