"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { useTranslations, useLocale } from "next-intl";
import Layout from "../components/Layout";
import GameCard from "../components/GameCard";
import { fetchGamesData, GameListItem } from "../lib/games/api";
import { useExtractGamesData } from "../lib/games/utils";

export default function HomePage() {
  const locale = useLocale() as string;
  const t = useTranslations();
  
  // 状态管理 - 修改为使用GameListItem类型
  const [apiGamesData, setApiGamesData] = useState<GameListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 从API获取游戏数据
  useEffect(() => {
    async function loadGamesData() {
      setLoading(true);
      try {
        const data = await fetchGamesData();
        setApiGamesData(data);
      } catch (err) {
        setError("加载游戏数据失败");
        console.error(err);
      } finally {
        setLoading(false);
      }
    }
    
    loadGamesData();
  }, []);
  
  // 根据当前语言提取游戏数据
  const games = useExtractGamesData(apiGamesData);
  
  return (
    <Layout>
      {/* 英雄区域 */}
      <section className="relative h-[500px] rounded-3xl overflow-hidden mb-16">
        {/* <Image
          src="/images/hero-bg.jpg"
          alt="Hero background"
          fill
          className="object-cover"
          priority
        /> */}
        <div className="absolute inset-0 bg-gradient-to-r from-black/80 to-transparent" />
        <div className="absolute inset-0 flex flex-col justify-center px-8 md:px-12 lg:px-16">
          <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white max-w-2xl mb-4">
            {t('home.hero.title')}
          </h1>
          <p className="text-lg text-zinc-300 max-w-xl mb-8">
            {t('home.hero.subtitle')}
          </p>
          <div>
            <Link
              href="#games"
              className="px-6 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-medium rounded-lg transition-colors"
            >
              {t('home.hero.cta')}
            </Link>
          </div>
        </div>
      </section>

      {/* 特点区域 */}
      <section className="mb-16">
        <h2 className="text-2xl font-bold text-white mb-8 text-center">
          {t('home.features.title')}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* 特点 1 */}
          <div className="bg-zinc-800/50 backdrop-blur-sm border border-zinc-700/30 rounded-xl p-6">
            <div className="w-12 h-12 bg-indigo-500/20 rounded-lg flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-indigo-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {t('home.features.security.title')}
            </h3>
            <p className="text-zinc-400">
              {t('home.features.security.description')}
            </p>
          </div>

          {/* 特点 2 */}
          <div className="bg-zinc-800/50 backdrop-blur-sm border border-zinc-700/30 rounded-xl p-6">
            <div className="w-12 h-12 bg-indigo-500/20 rounded-lg flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-indigo-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {t('home.features.instant.title')}
            </h3>
            <p className="text-zinc-400">
              {t('home.features.instant.description')}
            </p>
          </div>

          {/* 特点 3 */}
          <div className="bg-zinc-800/50 backdrop-blur-sm border border-zinc-700/30 rounded-xl p-6">
            <div className="w-12 h-12 bg-indigo-500/20 rounded-lg flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-indigo-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"
                />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-white mb-2">
              {t('home.features.payment.title')}
            </h3>
            <p className="text-zinc-400">
              {t('home.features.payment.description')}
            </p>
          </div>
        </div>
      </section>

      {/* 游戏列表区域 */}
      <section id="games" className="mb-16">
        <h2 className="text-2xl font-bold text-white mb-8">
          {t('home.popularGames')}
        </h2>
        
        {loading && (
          <div className="flex flex-col items-center justify-center py-12">
            <div className="w-12 h-12 border-4 border-indigo-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="mt-4 text-zinc-400">{t('ui.loading')}</p>
          </div>
        )}
        
        {error && (
          <div className="bg-red-900/20 border border-red-700/30 rounded-lg p-4 mb-6">
            <p className="text-red-400">{error}</p>
          </div>
        )}
        
        {!loading && games.length === 0 && !error && (
          <div className="bg-zinc-800/50 backdrop-blur-sm border border-zinc-700/30 rounded-xl p-6 text-center">
            <p className="text-zinc-400">{t('ui.noGamesFound')}</p>
          </div>
        )}
        
        {!loading && games.length > 0 && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {games.map((game) => (
              <Link key={game.id} href={`/${locale}/games/${game.id}`}>
                <GameCard
                  id={game.id}
                  imageUrl={game.imageUrl || "/images/placeholder.jpg"}
                  title={game.title || game.id}
                  category={game.category || "游戏"}
                  rating={game.rating}
                  playerCount={game.playerCount}
                />
              </Link>
            ))}
          </div>
        )}
      </section>

      {/* 促销区域 */}
      <section className="bg-gradient-to-r from-indigo-900/30 to-purple-900/30 border border-indigo-700/30 rounded-2xl p-8 mb-16">
        <div className="flex flex-col md:flex-row items-center justify-between">
          <div className="mb-6 md:mb-0">
            <h2 className="text-2xl font-bold text-white mb-2">
              {t('home.promo.title')}
            </h2>
            <p className="text-zinc-300 max-w-xl">
              {t('home.promo.description')}
            </p>
          </div>
          <Link
            href="#games"
            className="px-6 py-3 bg-white text-indigo-900 font-medium rounded-lg hover:bg-zinc-100 transition-colors"
          >
            {t('home.promo.cta')}
          </Link>
        </div>
      </section>
    </Layout>
  );
} 