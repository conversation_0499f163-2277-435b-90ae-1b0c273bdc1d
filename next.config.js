import withNextIntl from 'next-intl/plugin';

/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    domains: ['localhost'],
    unoptimized: true, // 禁用图片优化，直接使用原始图片
  },
  typescript: {
    ignoreBuildErrors: true, // 忽略构建时的TypeScript错误
  },
  eslint: {
    ignoreDuringBuilds: true, // 忽略构建时的ESLint错误
  }
};

export default withNextIntl('./src/app/i18n/request.ts')(nextConfig);

 