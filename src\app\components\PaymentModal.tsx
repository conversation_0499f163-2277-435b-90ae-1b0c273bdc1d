"use client";

import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import * as Dialog from "@radix-ui/react-dialog";
import * as Tabs from "@radix-ui/react-tabs";
import { Product, Game, UserInfo } from "../lib/games/types";
import { getCurrencySymbol } from "../lib/games/utils";
import { doPayment } from "../lib/games/api";

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
  game: Game;
  userInfo: UserInfo | null;
  locale: string;
  paymentUrl?: string | null;
  onConfirmPayment?: () => void;
}

type PaymentMethodType = "wechat" | "alipay" | "creditcard";

export default function PaymentModal({ 
  isOpen, 
  onClose, 
  product, 
  game, 
  userInfo, 
  locale,
  paymentUrl,
  onConfirmPayment 
}: PaymentModalProps) {
  const [paymentMethod, setPaymentMethod] = useState<PaymentMethodType>("wechat");
  const [paymentStatus, setPaymentStatus] = useState<"pending" | "processing" | "success" | "error">("pending");
  const [error, setError] = useState<string | null>(null);
  const scrollYRef = useRef(0);
  
  // 阻止背景滚动 - 改进版
  useEffect(() => {
    const handleScroll = (e: TouchEvent) => {
      if (isOpen) {
        e.preventDefault();
      }
    };
    
    if (isOpen) {
      // 记录当前滚动位置
      scrollYRef.current = window.scrollY;
      
      // 固定body
      document.body.style.position = 'fixed';
      document.body.style.top = `-${scrollYRef.current}px`;
      document.body.style.width = '100%';
      document.body.style.overflowY = 'hidden';
      
      // 添加触摸事件监听器
      document.addEventListener('touchmove', handleScroll, { passive: false });
    } else {
      // 恢复body状态
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflowY = '';
      
      // 恢复滚动位置
      window.scrollTo(0, scrollYRef.current);
      
      // 移除触摸事件监听器
      document.removeEventListener('touchmove', handleScroll);
    }
    
    return () => {
      // 确保清理
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflowY = '';
      document.removeEventListener('touchmove', handleScroll);
    };
  }, [isOpen]);
  
  // 安全获取货币符号
  const getCurrencySymbolSafe = () => {
    if (product?.currency) {
      return getCurrencySymbol(product.currency);
    } else if (game?.currency) {
      return getCurrencySymbol(game.currency);
    }
    return '¥'; // 默认使用人民币符号
  };
  
  const handlePayment = async () => {
    if (!product || !userInfo) {
      setError("请先登录");
      return;
    }

    // 检查是否已选择区服和角色
    if (!userInfo.serverId || !userInfo.roleId || userInfo.serverId === '0' || userInfo.roleId === '0') {
      setError("请先选择区服和角色后再进行支付");
      return;
    }

    // 检查是否有productCode
    if (!userInfo.productCode) {
      setError("缺少productCode，请重新登录获取站点信息");
      return;
    }

    setPaymentStatus("processing");
    setError(null);

    try {
      // 获取语言设置
      let ulang;
      switch (locale) {
        case 'ko':
          ulang = 'en-kor';
          break;
        case 'en':
          ulang = 'en';
          break;
        case 'zh':
          ulang = 'zh-cn';
          break;
        default:
          ulang = 'zh-cn';
      }

      const response = await doPayment({
        productCode: userInfo.productCode,
        goodsId: product.skuCode || product.id,
        subject: product.title || product.id,
        roleId: userInfo.roleId,
        serverId: userInfo.serverId,
        roleName: userInfo.roleName,
        uid: userInfo.uid.toString(),
        ulang: ulang
      });

      if (response.status && response.data.payUrl) {
        // 跳转到支付页面
        window.location.href = response.data.payUrl;
      } else {
        throw new Error(response.message || '支付请求失败');
      }
    } catch (err) {
      console.error('支付失败:', err);
      setPaymentStatus("error");
      setError(err instanceof Error ? err.message : '支付请求失败');
    }
  };
  
  // 如果有支付链接，显示确认页面
  if (paymentUrl) {
    return (
      <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
        <Dialog.Portal>
          <Dialog.Overlay 
            className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40" 
            style={{ touchAction: 'none' }}
            onClick={(e) => e.stopPropagation()}
          />
          <Dialog.Content 
            className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md max-h-[85vh] overflow-auto rounded-2xl bg-gradient-to-br from-zinc-900 to-zinc-950 border border-zinc-800 shadow-2xl z-50 p-6"
            style={{ touchAction: 'pan-y' }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex flex-col items-center">
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10 text-green-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-4">确认支付</h3>
              <div className="w-full p-4 bg-zinc-800/50 border border-zinc-700/30 rounded-lg mb-6">
                <p className="text-sm text-zinc-400 mb-2">支付金额</p>
                <p className="text-2xl font-bold text-white">{getCurrencySymbolSafe()}{product?.price || 0}</p>
              </div>
              <div className="flex gap-4 w-full">
                <button
                  onClick={onClose}
                  className="flex-1 py-3 px-4 bg-zinc-800 text-zinc-400 font-medium rounded-lg hover:bg-zinc-700 transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={onConfirmPayment}
                  className="flex-1 py-3 px-4 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white font-medium rounded-lg transition-colors"
                >
                  确认支付
                </button>
              </div>
            </div>
          </Dialog.Content>
        </Dialog.Portal>
      </Dialog.Root>
    );
  }

  // 如果没有产品或游戏数据，不渲染模态框
  if (!product || !game) {
    return null;
  }
  
  return (
    <Dialog.Root open={isOpen} onOpenChange={(open) => !open && onClose()}>
      <Dialog.Portal>
        <Dialog.Overlay 
          className="fixed inset-0 bg-black/60 backdrop-blur-sm z-40" 
          style={{ touchAction: 'none' }}
          onClick={(e) => e.stopPropagation()}
        />
        <Dialog.Content 
          className="fixed left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-md max-h-[85vh] overflow-auto rounded-2xl bg-gradient-to-br from-zinc-900 to-zinc-950 border border-zinc-800 shadow-2xl z-50 p-6"
          style={{ touchAction: 'pan-y' }}
          onClick={(e) => e.stopPropagation()}
        >
          {paymentStatus === "pending" && (
            <div
              className="transition-opacity duration-300 opacity-100"
              style={{ animation: "fadeIn 0.3s ease-out" }}
            >
              <div className="flex justify-between items-start mb-6">
                <Dialog.Title className="text-xl font-bold text-white">
                  确认支付
                </Dialog.Title>
                <Dialog.Close asChild>
                  <button
                    className="rounded-full p-1 hover:bg-zinc-800 transition-colors"
                    aria-label="关闭"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-zinc-400"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M6 18L18 6M6 6l12 12"
                      />
                    </svg>
                  </button>
                </Dialog.Close>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg">
                  <p className="text-sm text-red-400">{error}</p>
                </div>
              )}

              <div className="flex items-center space-x-4 p-4 rounded-xl bg-zinc-800/50 border border-zinc-700/30">
                <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center">
                  <Image
                    src={product?.imageUrl || '/images/products/default.png'}
                    alt={product?.title || '产品'}
                    width={48}
                    height={48}
                    className="object-contain"
                  />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-white">{product?.title || '未命名产品'}</h3>
                  <p className="text-sm text-zinc-400">{game?.title || '未命名游戏'}</p>
                  <p className="text-xl font-bold text-white mt-1">{getCurrencySymbolSafe()}{product?.price || 0}</p>
                </div>
              </div>

              <div className="mb-6">
                <h3 className="text-sm font-medium text-zinc-400 mb-3">选择支付方式</h3>
                <Tabs.Root
                  value={paymentMethod}
                  onValueChange={(value) => setPaymentMethod(value as PaymentMethodType)}
                >
                  <Tabs.List className="flex space-x-2 mb-4">
                    <Tabs.Trigger
                      value="wechat"
                      className={`flex-1 py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-all ${
                        paymentMethod === "wechat"
                          ? "bg-green-500/20 border border-green-500/50 text-green-400"
                          : "bg-zinc-800/50 border border-zinc-700/30 text-zinc-400 hover:bg-zinc-800"
                      }`}
                    >
                      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M8.69 13.09c-.4 0-.73-.33-.73-.73 0-.4.33-.73.73-.73.4 0 .73.33.73.73 0 .4-.33.73-.73.73zm4.4-2.53c.4 0 .73.33.73.73 0 .4-.33.73-.73.73-.4 0-.73-.33-.73-.73 0-.4.33-.73.73-.73zm-4.4 2.53c-.4 0-.73-.33-.73-.73 0-.4.33-.73.73-.73.4 0 .73.33.73.73 0 .4-.33.73-.73.73zm4.4-2.53c.4 0 .73.33.73.73 0 .4-.33.73-.73.73-.4 0-.73-.33-.73-.73 0-.4.33-.73.73-.73zm1.95 5.88c0-2.98-2.98-5.4-6.65-5.4s-6.65 2.42-6.65 5.4c0 2.98 2.98 5.4 6.65 5.4 0 0 .83-.04 1.4-.11l1.26.7s.19.11.34.11c.24 0 .43-.27.34-.54l-.32-1.07c.96-.54 2.99-1.97 2.99-4.39 0 0 .64 0 .64-.6zm-11.62 0c0-2.47 2.44-4.47 5.45-4.47s5.45 2 5.45 4.47c0 2.47-2.44 4.47-5.45 4.47-.46 0-.91-.04-1.33-.11l-.12-.02-1.47.82-.03-.1-.23-.77-.09-.33-.31.19c-1.13-.64-1.87-1.85-1.87-3.15zm13.62 4.29l.45 1.53c.12.4-.15.8-.54.8-.15 0-.31-.05-.43-.15l-1.8-1.02c-.67.1-1.34.15-2.01.15-4.34 0-7.85-2.95-7.85-6.6 0-3.66 3.51-6.6 7.85-6.6s7.85 2.95 7.85 6.6c0 2.86-1.75 5.29-4.25 6.34h-.01c0-.01-.01-.02-.01-.03 0-.01-.01-.01-.01-.02h-.25z" />
                      </svg>
                      <span>微信支付</span>
                    </Tabs.Trigger>
                    <Tabs.Trigger
                      value="alipay"
                      className={`flex-1 py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-all ${
                        paymentMethod === "alipay"
                          ? "bg-blue-500/20 border border-blue-500/50 text-blue-400"
                          : "bg-zinc-800/50 border border-zinc-700/30 text-zinc-400 hover:bg-zinc-800"
                      }`}
                    >
                      <svg className="w-5 h-5" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M21.422 15.358c-4.375.751-7.498-1.963-8.5-3.625 2.106-.626 3.256-.25 4.25.126 1.448.546 2.25.293 3.25-.376-.707-.931-1.697-1.673-2.75-2.126.625-3.376-1.666-5.608-4.001-6.608C10.051 1.384 5.026 2.627 3.75 5.88c-1.25 3.25.376 6.75 2.875 8.5-1.125.75-2.25 1.75-2.625 3.126-.375 1.374.25 2.75 1.25 3.624 1.5 1.376 3.75 1.626 5.75 1 2-.625 3.5-2.126 4.375-3.75 1.124.374 2.124.75 3.124 *********** 1.626.625 2.376.874.75.25 1.5.5 2.25.75-.125-1.75-.375-3.5-.703-5.772zm-1.172 4.892c-.75-.25-1.5-.5-2.25-.75-.75-.25-1.626-.625-2.376-.874-.25-.126-.625-.25-.875-.376 1.626-2.75 1.75-5.126.375-6.75-1.124-1.376-2.874-1.876-4.624-1.626-1.75.25-3.25 1.25-4.126 2.626-.874 1.374-.624 3.124.5 4.25 1.126 1.126 2.75 1.5 4.376 1.126 1.626-.376 3.124-1.376 4.5-2.876.75.25 1.376.626 2 1 .626.376 1.126.75 1.626 1.126.5.376 1 .75 1.5 1.126.25 1.126.5 2.376.75 3.5-.375-.25-.876-.5-1.376-.752z" />
                      </svg>
                      <span>支付宝</span>
                    </Tabs.Trigger>
                    <Tabs.Trigger
                      value="creditcard"
                      className={`flex-1 py-3 px-4 rounded-lg flex items-center justify-center space-x-2 transition-all ${
                        paymentMethod === "creditcard"
                          ? "bg-purple-500/20 border border-purple-500/50 text-purple-400"
                          : "bg-zinc-800/50 border border-zinc-700/30 text-zinc-400 hover:bg-zinc-800"
                      }`}
                    >
                      <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                      </svg>
                      <span>银行卡</span>
                    </Tabs.Trigger>
                  </Tabs.List>

                  <Tabs.Content value="wechat" className="rounded-xl bg-zinc-800/50 border border-zinc-700/30 p-4">
                    <div className="flex flex-col items-center">
                      <div className="w-48 h-48 bg-white p-2 rounded-lg mb-4">
                        <div className="w-full h-full bg-zinc-100 flex items-center justify-center">
                          <span className="text-xs text-zinc-500">微信支付二维码</span>
                        </div>
                      </div>
                      <p className="text-sm text-zinc-400 text-center mb-2">
                        请使用微信扫描二维码完成支付
                      </p>
                    </div>
                  </Tabs.Content>

                  <Tabs.Content value="alipay" className="rounded-xl bg-zinc-800/50 border border-zinc-700/30 p-4">
                    <div className="flex flex-col items-center">
                      <div className="w-48 h-48 bg-white p-2 rounded-lg mb-4">
                        <div className="w-full h-full bg-zinc-100 flex items-center justify-center">
                          <span className="text-xs text-zinc-500">支付宝二维码</span>
                        </div>
                      </div>
                      <p className="text-sm text-zinc-400 text-center mb-2">
                        请使用支付宝扫描二维码完成支付
                      </p>
                    </div>
                  </Tabs.Content>

                  <Tabs.Content value="creditcard" className="rounded-xl bg-zinc-800/50 border border-zinc-700/30 p-4">
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-zinc-400 mb-1">
                          卡号
                        </label>
                        <input
                          type="text"
                          placeholder="1234 5678 9012 3456"
                          className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-zinc-400 mb-1">
                            有效期
                          </label>
                          <input
                            type="text"
                            placeholder="MM/YY"
                            className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-zinc-400 mb-1">
                            CVV
                          </label>
                          <input
                            type="text"
                            placeholder="123"
                            className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                          />
                        </div>
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-zinc-400 mb-1">
                          持卡人姓名
                        </label>
                        <input
                          type="text"
                          placeholder="张三"
                          className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-purple-500/50"
                        />
                      </div>
                    </div>
                  </Tabs.Content>
                </Tabs.Root>
              </div>

              <button
                onClick={handlePayment}
                className={`w-full py-3 px-4 font-medium rounded-lg transition-colors flex items-center justify-center ${
                  !userInfo || !userInfo.serverId || !userInfo.roleId || userInfo.serverId === '0' || userInfo.roleId === '0' || !userInfo.productCode
                    ? "bg-zinc-800 text-zinc-500 cursor-not-allowed"
                    : "bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white"
                }`}
                disabled={!userInfo || !userInfo.serverId || !userInfo.roleId || userInfo.serverId === '0' || userInfo.roleId === '0' || !userInfo.productCode}
              >
                {!userInfo
                  ? "请先登录"
                  : !userInfo.productCode
                    ? "缺少productCode，请重新登录"
                    : (!userInfo.serverId || !userInfo.roleId || userInfo.serverId === '0' || userInfo.roleId === '0')
                      ? "请先选择区服和角色"
                      : `确认支付 ${getCurrencySymbolSafe()}{product?.price || 0}`
                }
              </button>
            </div>
          )}

          {paymentStatus === "processing" && (
            <div
              className="flex flex-col items-center justify-center py-12 transition-opacity duration-300 opacity-100"
              style={{ animation: "fadeIn 0.3s ease-out" }}
            >
              <div className="w-16 h-16 border-4 border-indigo-500/30 border-t-indigo-500 rounded-full animate-spin mb-6"></div>
              <h3 className="text-xl font-bold text-white mb-2">处理中</h3>
              <p className="text-zinc-400 text-center">
                正在处理您的支付请求，请稍候...
              </p>
            </div>
          )}

          {paymentStatus === "success" && (
            <div
              className="flex flex-col items-center justify-center py-12 transition-opacity duration-300 opacity-100"
              style={{ animation: "fadeIn 0.3s ease-out" }}
            >
              <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10 text-green-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">支付成功</h3>
              <p className="text-zinc-400 text-center mb-6">
                您的支付已成功处理，商品已添加到您的账户
              </p>
              <button
                onClick={onClose}
                className="py-2 px-4 bg-green-500/20 text-green-400 font-medium rounded-lg hover:bg-green-500/30 transition-colors"
              >
                完成
              </button>
            </div>
          )}

          {paymentStatus === "error" && (
            <div
              className="flex flex-col items-center justify-center py-12 transition-opacity duration-300 opacity-100"
              style={{ animation: "fadeIn 0.3s ease-out" }}
            >
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mb-6">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-10 w-10 text-red-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">支付失败</h3>
              <p className="text-zinc-400 text-center mb-6">
                处理您的支付时发生错误，请稍后重试
              </p>
              <div className="flex space-x-4">
                <button
                  onClick={() => setPaymentStatus("pending")}
                  className="py-2 px-4 bg-zinc-800 text-white font-medium rounded-lg hover:bg-zinc-700 transition-colors"
                >
                  重试
                </button>
                <button
                  onClick={onClose}
                  className="py-2 px-4 bg-zinc-800 text-zinc-400 font-medium rounded-lg hover:bg-zinc-700 transition-colors"
                >
                  取消
                </button>
              </div>
            </div>
          )}
        </Dialog.Content>
      </Dialog.Portal>
    </Dialog.Root>
  );
} 