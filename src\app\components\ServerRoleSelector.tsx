"use client";

import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";
import { ServerRoleInfo } from "../lib/games/types";

interface ServerRoleSelectorProps {
  serverRoleList: ServerRoleInfo[];
  onSelect: (serverId: string, roleId: string, roleName: string) => void;
  selectedServerId?: string;
  selectedRoleId?: string;
}

export default function ServerRoleSelector({ 
  serverRoleList, 
  onSelect, 
  selectedServerId, 
  selectedRoleId 
}: ServerRoleSelectorProps) {
  const [currentServerId, setCurrentServerId] = useState<string>(selectedServerId || '');
  const [currentRoleId, setCurrentRoleId] = useState<string>(selectedRoleId || '');
  const t = useTranslations();

  // 获取唯一的服务器列表
  const uniqueServers = serverRoleList.reduce((acc, item) => {
    if (!acc.find(server => server.serverId === item.serverId)) {
      acc.push({
        serverId: item.serverId,
        serverName: item.serverName
      });
    }
    return acc;
  }, [] as { serverId: string; serverName: string }[]);

  // 根据选中的服务器获取角色列表
  const rolesForSelectedServer = serverRoleList.filter(
    item => item.serverId === currentServerId
  );

  // 当服务器改变时，重置角色选择
  useEffect(() => {
    if (currentServerId && rolesForSelectedServer.length > 0) {
      // 如果当前选中的角色不在新服务器的角色列表中，选择第一个角色
      const roleExists = rolesForSelectedServer.find(role => role.roleId === currentRoleId);
      if (!roleExists) {
        const firstRole = rolesForSelectedServer[0];
        setCurrentRoleId(firstRole.roleId);
        onSelect(currentServerId, firstRole.roleId, firstRole.roleName);
      }
    }
  }, [currentServerId, rolesForSelectedServer, currentRoleId, onSelect]);

  const handleServerChange = (serverId: string) => {
    setCurrentServerId(serverId);
    // 自动选择该服务器的第一个角色
    const rolesForServer = serverRoleList.filter(item => item.serverId === serverId);
    if (rolesForServer.length > 0) {
      const firstRole = rolesForServer[0];
      setCurrentRoleId(firstRole.roleId);
      onSelect(serverId, firstRole.roleId, firstRole.roleName);
    }
  };

  const handleRoleChange = (roleId: string) => {
    setCurrentRoleId(roleId);
    const selectedRole = rolesForSelectedServer.find(role => role.roleId === roleId);
    if (selectedRole) {
      onSelect(currentServerId, roleId, selectedRole.roleName);
    }
  };

  if (serverRoleList.length === 0) {
    return (
      <div className="text-center py-4">
        <p className="text-zinc-400">{t('serverRole.noData')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-zinc-400 mb-2">
          {t('serverRole.selectServer')}
        </label>
        <select
          value={currentServerId}
          onChange={(e) => handleServerChange(e.target.value)}
          className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500/50"
        >
          <option value="">{t('serverRole.pleaseSelectServer')}</option>
          {uniqueServers.map((server) => (
            <option key={server.serverId} value={server.serverId}>
              {server.serverName}
            </option>
          ))}
        </select>
      </div>

      {currentServerId && rolesForSelectedServer.length > 0 && (
        <div>
          <label className="block text-sm font-medium text-zinc-400 mb-2">
            {t('serverRole.selectRole')}
          </label>
          <select
            value={currentRoleId}
            onChange={(e) => handleRoleChange(e.target.value)}
            className="w-full px-3 py-2 bg-zinc-900 border border-zinc-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500/50"
          >
            <option value="">{t('serverRole.pleaseSelectRole')}</option>
            {rolesForSelectedServer.map((role) => (
              <option key={role.roleId} value={role.roleId}>
                {role.roleName} ({t('serverRole.level')}: {role.roleLevel})
              </option>
            ))}
          </select>
        </div>
      )}

      {currentServerId && currentRoleId && (
        <div className="p-3 bg-indigo-500/20 border border-indigo-500/30 rounded-lg">
          <p className="text-indigo-300 text-sm">
            {t('serverRole.selected')}: {uniqueServers.find(s => s.serverId === currentServerId)?.serverName} - {' '}
            {rolesForSelectedServer.find(r => r.roleId === currentRoleId)?.roleName}
          </p>
        </div>
      )}
    </div>
  );
}
