# 游戏支付门户 - API多语言数据方案

本文档介绍了如何通过API返回多语言游戏数据的实现方案。

## 方案概述

在这个方案中，后端API直接返回包含多语言文本的游戏数据，前端根据当前语言环境提取相应的文本进行展示。

### 优点

1. **减少API请求**：一次请求获取所有语言的数据，无需为每种语言单独请求
2. **前端切换语言无需重新请求**：语言切换时可以直接从缓存的数据中提取新语言的文本
3. **数据一致性**：确保所有语言版本的数据结构一致
4. **简化后端逻辑**：后端不需要根据语言参数动态生成不同的响应

### 缺点

1. **响应体积增大**：包含所有语言的数据会使响应体积增大
2. **不适合文本量大的场景**：如果有大量需要翻译的文本，可能需要考虑其他方案

## 数据结构

### API返回的数据结构

```typescript
// 游戏数据（包含多语言）
interface ApiGame {
  id: string;
  bannerUrl: string;
  imageUrl?: string;
  rating: number;
  playerCount: string;
  i18n: {
    title: {
      zh: string;
      en: string;
      ko: string;
    };
    description: {
      zh: string;
      en: string;
      ko: string;
    };
    category: {
      zh: string;
      en: string;
      ko: string;
    };
  };
  products: Array<{
    id: string;
    imageUrl: string;
    price: number;
    originalPrice?: number;
    i18n: {
      title: {
        zh: string;
        en: string;
        ko: string;
      };
      description: {
        zh: string;
        en: string;
        ko: string;
      };
    };
  }>;
}
```

### 前端使用的数据结构（当前语言）

```typescript
// 前端使用的游戏数据（当前语言）
interface Game {
  id: string;
  bannerUrl: string;
  imageUrl?: string;
  rating: number;
  playerCount: string;
  title: string;
  description: string;
  category: string;
  products: Array<{
    id: string;
    imageUrl: string;
    price: number;
    originalPrice?: number;
    title: string;
    description: string;
  }>;
}
```

## 实现步骤

### 1. 定义数据类型

在 `src/app/lib/games/types.ts` 中定义API数据类型和前端使用的数据类型。

### 2. 创建API服务

在 `src/app/lib/games/api.ts` 中创建获取游戏数据的API服务。

### 3. 创建数据处理工具

在 `src/app/lib/games/utils.ts` 中创建从API数据中提取当前语言文本的工具函数。

### 4. 在组件中使用

```tsx
// 在React组件中使用
import { useState, useEffect } from "react";
import { useLocale } from "next-intl";
import { fetchGameData } from "../lib/games/api";
import { useExtractGameData } from "../lib/games/utils";

function GameDetail({ gameId }) {
  const [apiGameData, setApiGameData] = useState(null);
  
  useEffect(() => {
    async function loadData() {
      const data = await fetchGameData(gameId);
      setApiGameData(data);
    }
    loadData();
  }, [gameId]);
  
  // 根据当前语言提取游戏数据
  const game = useExtractGameData(apiGameData);
  
  if (!game) return <div>加载中...</div>;
  
  return (
    <div>
      <h1>{game.title}</h1>
      <p>{game.description}</p>
      {/* 其他UI元素 */}
    </div>
  );
}
```

## 添加新语言

要添加新的语言支持，需要：

1. 在 `src/app/lib/games/types.ts` 中更新 `SupportedLocale` 类型
2. 在API响应的 `i18n` 对象中添加新语言的文本
3. 更新语言切换组件，添加新语言选项

## 添加新游戏

要添加新游戏，只需在API响应中添加新的游戏对象，确保包含所有支持语言的文本。

## 注意事项

1. **数据缓存**：考虑在前端缓存API返回的数据，减少重复请求
2. **错误处理**：处理API请求失败的情况，提供友好的错误提示
3. **回退策略**：当某种语言的文本缺失时，可以回退到默认语言（如英语）
4. **延迟加载**：对于大型应用，可以考虑按需加载游戏数据，而不是一次性加载所有游戏

## 示例代码

完整的示例代码可以在以下文件中找到：

- `src/app/lib/games/types.ts`：数据类型定义
- `src/app/lib/games/mock-api-data.ts`：模拟的API数据
- `src/app/lib/games/api.ts`：API服务
- `src/app/lib/games/utils.ts`：数据处理工具
- `src/app/[locale]/games/[gameId]/page-with-api.tsx`：游戏详情页面
- `src/app/[locale]/page-with-api.tsx`：首页 