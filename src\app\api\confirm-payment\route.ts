import { NextRequest, NextResponse } from 'next/server';
import { APP_API_BASE_URL } from '../../config/api';

export async function POST(request: NextRequest) {
  try {
    // 获取请求体
    const body = await request.json();
    
    console.log('确认支付API代理 - 请求参数:', body);

    // 调用后端API
    const response = await fetch(`${APP_API_BASE_URL}/pay/confirm-payment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    });

    const data = await response.json();
    
    console.log('确认支付API代理 - 后端响应:', data);

    // 返回响应
    return NextResponse.json(data, {
      status: response.status
    });
  } catch (error) {
    console.error('确认支付API代理错误:', error);
    
    return NextResponse.json(
      {
        code: -1,
        msg: '确认支付请求失败',
        data: null
      },
      {
        status: 500
      }
    );
  }
}

// 处理CORS预检请求（在nginx反代环境下通常不需要）
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200
  });
}
