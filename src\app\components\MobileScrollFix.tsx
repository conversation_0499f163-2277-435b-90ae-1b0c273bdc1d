"use client";

import { useEffect } from 'react';

/**
 * 此组件专门用于修复移动设备上的滚动问题，特别是iOS上的橡皮筋效果
 */
export default function MobileScrollFix() {
  useEffect(() => {
    // 检测是否为移动设备
    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);

    if (!isMobile) return;

    // 特别是针对iOS的修复
    const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);

    // iOS特殊处理 - 只设置必要的样式，不阻止正常滚动
    if (isIOS) {
      // 防止页面在滚动到顶部或底部时的弹性效果
      document.body.style.overscrollBehavior = 'none';
      document.documentElement.style.overscrollBehavior = 'none';

      // 添加标记类，用于CSS选择器
      document.documentElement.classList.add('ios-device');
    }

    // 通用移动设备处理 - 只设置基本的滚动优化
    document.body.style.webkitOverflowScrolling = 'touch';

    return () => {
      if (isIOS) {
        document.body.style.overscrollBehavior = '';
        document.documentElement.style.overscrollBehavior = '';
        document.documentElement.classList.remove('ios-device');
      }

      document.body.style.webkitOverflowScrolling = '';
    };
  }, []);

  // 这个组件不渲染任何内容，只添加事件处理程序
  return null;
}