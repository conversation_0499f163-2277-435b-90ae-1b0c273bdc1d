{"games": {"pubg-mobile": {"title": "배틀그라운드 모바일", "description": "배틀그라운드 모바일은 텐센트 게임즈에서 개발한 멀티플레이어 온라인 배틀로얄 게임으로, 플레이어는 지속적으로 축소되는 맵에서 서로 싸워 마지막까지 살아남는 사람이 되어야 합니다.", "category": "슈팅", "products": {"pubg-uc-60": {"title": "60 UC", "description": "게임 내 아이템 및 의상 구매에 사용 가능"}, "pubg-uc-300": {"title": "300 UC", "description": "게임 내 아이템 및 의상 구매에 사용 가능"}, "pubg-uc-600": {"title": "600 UC", "description": "게임 내 아이템 및 의상 구매에 사용 가능"}, "pubg-uc-1500": {"title": "1500 UC", "description": "게임 내 아이템 및 의상 구매에 사용 가능"}, "pubg-uc-3000": {"title": "3000 UC", "description": "게임 내 아이템 및 의상 구매에 사용 가능"}, "pubg-royale-pass": {"title": "엘리트 로얄 패스", "description": "추가 게임 내 보상 및 도전 과제 잠금 해제"}}}, "honor-of-kings": {"title": "왕자영요", "description": "왕자영요는 텐센트 게임즈에서 개발한 멀티플레이어 온라인 배틀 아레나 게임으로, 플레이어는 다양한 영웅 캐릭터를 선택하고 팀원과 협력하여 상대 팀을 물리쳐야 합니다.", "category": "MOBA", "products": {"hok-diamond-60": {"title": "60 다이아몬드", "description": "게임 내 영웅 및 스킨 구매에 사용 가능"}, "hok-diamond-300": {"title": "300 다이아몬드", "description": "게임 내 영웅 및 스킨 구매에 사용 가능"}, "hok-diamond-600": {"title": "600 다이아몬드", "description": "게임 내 영웅 및 스킨 구매에 사용 가능"}, "hok-diamond-1500": {"title": "1500 다이아몬드", "description": "게임 내 영웅 및 스킨 구매에 사용 가능"}, "hok-diamond-3000": {"title": "3000 다이아몬드", "description": "게임 내 영웅 및 스킨 구매에 사용 가능"}, "hok-battle-pass": {"title": "영광의 패스", "description": "추가 게임 내 보상 및 도전 과제 잠금 해제"}}}}, "ui": {"backToHome": "홈으로 돌아가기", "gameNotFound": "게임을 찾을 수 없음", "gameNotFoundDesc": "요청하신 게임을 찾을 수 없습니다.", "gameIntro": "게임 소개", "selectRechargeItem": "충전 항목 선택", "selected": "선택됨", "pleaseSelect": "충전 항목을 선택하세요", "payNow": "지금 결제", "login": "로그인"}, "login": {"title": "게임 로그인", "username": "사용자 이름", "password": "비밀번호", "usernamePlaceholder": "사용자 이름을 입력하세요", "passwordPlaceholder": "비밀번호를 입력하세요", "loginButton": "로그인", "loggingIn": "로그인 중...", "close": "닫기", "usernameRequired": "사용자 이름을 입력해 주세요", "passwordRequired": "비밀번호를 입력해 주세요", "invalidCredentials": "로그인 실패, 사용자 이름과 비밀번호를 확인하세요", "serverError": "로그인 실패, 서버 응답 오류", "networkError": "로그인 실패, 네트워크 연결을 확인하세요", "welcome": "환영합니다", "logout": "로그아웃"}}