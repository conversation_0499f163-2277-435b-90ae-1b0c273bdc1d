# 游戏支付平台

这是一个用于游戏充值和支付的多语言网站平台。

## 功能特点

- 响应式设计，适配各种设备
- 多语言支持 (中文、英文、韩文)
- 游戏列表展示
- 游戏详情页面
- 充值产品选择
- 支付流程模拟

## 技术栈

- Next.js 14 (App Router)
- React
- TypeScript
- Tailwind CSS
- next-intl (国际化)

## 国际化实现

本项目使用了 next-intl 库来实现国际化功能。实现策略如下：

### 路由结构

- 使用 `/[locale]/...` 路由结构，支持不同语言的URL路径
- 通过中间件自动检测用户浏览器语言并重定向到相应的语言路径
- 默认语言为中文 (zh)

### 游戏数据国际化

采用了从API获取多语言数据的策略：

1. API返回的游戏数据包含多语言字段 (`i18n` 属性)
2. 前端根据当前语言环境提取相应的语言内容
3. 使用 `useExtractGameData` 和 `useExtractGamesData` hooks 处理国际化数据

### 界面文本国际化

- 所有界面文本存储在 `/messages/{locale}.json` 文件中
- 使用 `useTranslations` hook 获取当前语言的文本
- 支持嵌套的翻译键值结构

## 开发指南

### 安装依赖

```bash
npm install
```

### 运行开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看网站。

### 构建生产版本

```bash
npm run build
```

### 运行生产版本

```bash
npm start
```

## 项目结构

- `/src/app/[locale]` - 多语言页面组件
- `/src/app/components` - 共享组件
- `/src/app/lib` - 工具函数和数据
- `/messages` - 翻译文件
- `/public` - 静态资源

## 国际化测试

访问以下URL测试不同语言版本：

- 中文: [http://localhost:3000/zh](http://localhost:3000/zh)
- 英文: [http://localhost:3000/en](http://localhost:3000/en)
- 韩文: [http://localhost:3000/ko](http://localhost:3000/ko)

This is a [Next.js](https://nextjs.org) project bootstrapped with [`create-next-app`](https://nextjs.org/docs/app/api-reference/cli/create-next-app).

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
