# 游戏支付平台部署指南

这是一个基于 Next.js 15 构建的游戏支付平台前端应用程序。本文档提供部署和启动应用程序的详细步骤。

## 部署前提

确保服务器上已安装以下软件：

- Node.js 18+ (推荐 20+)
- npm 9+ (或 yarn)

## 部署步骤

### 1. 复制项目文件

将以下文件和目录复制到服务器上的部署目录：

```
.next/         # 构建输出目录
node_modules/  # 依赖库
public/        # 静态资源
package.json   # 项目配置
start.js       # 启动脚本
```

如果要从头开始部署，则只需复制以下文件：

```
.next/        # 构建输出目录
public/       # 静态资源
package.json  # 项目配置
start.js      # 启动脚本
```

然后执行 `npm install --production` 安装生产环境依赖。

### 2. 环境配置

创建一个 `.env.local` 文件在部署目录中，设置必要的环境变量：

```
# 端口设置
PORT=3000

# API设置
NEXT_PUBLIC_API_URL=https://your-api-url.com
```

根据实际部署环境调整相关配置。

### 3. 启动应用

有两种方式启动应用：

#### 方法一：使用 npm start

```bash
npm start
```

这将使用 Next.js 内置的生产服务器启动应用。

#### 方法二：使用启动脚本

```bash
node start.js
```

启动脚本会自动设置端口并启动应用。

### 4. 使用进程管理器（推荐）

在生产环境中，建议使用 PM2 等进程管理器来管理应用进程：

```bash
# 安装 PM2
npm install -g pm2

# 启动应用
pm2 start npm --name "game-payment-portal" -- start

# 或使用启动脚本
pm2 start start.js --name "game-payment-portal"
```

## 常见问题

1. **端口冲突**：如果遇到端口冲突，可以在启动前设置不同的端口：
   ```
   PORT=3001 npm start
   ```

2. **API 连接问题**：确保在 `.env.local` 文件中正确设置了 API 地址。

3. **内存不足**：对于资源受限的服务器，可以调整 Node.js 内存限制：
   ```
   NODE_OPTIONS="--max-old-space-size=512" npm start
   ```

## 更新部署

当需要更新应用时，只需替换 `.next` 目录和其他更改的文件，然后重启应用即可。

```bash
# 使用PM2重启应用
pm2 restart game-payment-portal
```

## 支持

如有任何部署问题，请联系技术支持团队获取帮助。 