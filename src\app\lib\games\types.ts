// 支持的语言类型
export type SupportedLocale = 'zh' | 'en' | 'ko';

// 国际化文本接口
export interface I18nText {
  zh: string;
  en: string;
  ko: string;
  [key: string]: string; // 支持其他可能的语言
}

// 产品国际化文本
export interface ProductI18n {
  title: I18nText;
  description: I18nText;
  [key: string]: I18nText; // 支持其他可能的国际化字段
}

// 游戏国际化文本
export interface GameI18n {
  title: I18nText;
  description: I18nText;
  category: I18nText;
  [key: string]: I18nText; // 支持其他可能的国际化字段
}

// 产品接口（API返回）
export interface ApiProduct {
  id: string;
  imageUrl: string;
  price: number;
  originalPrice?: number;
  currency?: string;
  i18n: ProductI18n;
  [key: string]: unknown; // 支持API返回的其他字段
}

// 游戏接口（API返回）
export interface ApiGame {
  id: string;
  bannerUrl: string;
  imageUrl?: string;
  rating: number;
  playerCount: string;
  currency?: string;
  i18n: GameI18n;
  products: ApiProduct[];
  [key: string]: unknown; // 支持API返回的其他字段
}

// 前端使用的产品接口（当前语言）
export interface Product {
  id: string;
  title?: string;
  description?: string;
  imageUrl?: string;
  price: number;
  originalPrice?: number;
  currency?: string;
  skuCode?: string;
}

// 前端使用的游戏接口（当前语言）
export interface Game {
  id: string;
  title?: string;
  description?: string;
  bannerUrl?: string;
  category?: string;
  rating?: number;
  playerCount?: number;
  currency: string;
  products: Product[];
  productCode?: string;
}

// 新的产品接口（使用 i18nObj）
export interface NewApiProduct extends Omit<ApiProduct, 'i18n'> {
  i18nObj: ProductI18n;
  [key: string]: unknown;
}

// 新的游戏接口（使用 i18nObj 和 productsArray）
export interface NewApiGame extends Omit<ApiGame, 'i18n' | 'products'> {
  i18nObj: GameI18n;
  productsArray: NewApiProduct[];
  [key: string]: unknown;
}

// 区服角色信息接口
export interface ServerRoleInfo {
  serverId: string;
  serverName: string;
  roleId: string;
  roleName: string;
  roleLevel: string;
  sdkUid: string;
}

// getSiteInfo API响应接口
export interface GetSiteInfoResponse {
  code: number;
  data: ServerRoleInfo[];
}

export interface UserInfo {
  uid: number;
  username: string;
  productId: number;
  gameId: string;
  isGuest: number;
  userStatus: number;
  roleId?: string;
  serverId?: string;
  roleName?: string;
  callbackUrl?: string;
  callbackKey?: string;
  productCode?: string;
}