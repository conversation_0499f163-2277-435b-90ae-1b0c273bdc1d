import { ReactNode } from "react";
import { getMessages } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";

interface LocaleLayoutProps {
  children: ReactNode;
  params: {
    locale: string;
  };
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  // 确保params被完全解析
  const resolvedParams = await Promise.resolve(params);
  // 获取locale参数
  const locale = resolvedParams?.locale || 'zh';
  
  try {
    // 获取对应语言的消息
    const messages = await getMessages({ locale });

    return (
      <NextIntlClientProvider locale={locale} messages={messages}>
        {children}
      </NextIntlClientProvider>
    );
  } catch (error) {
    console.error(`Error loading messages for locale ${locale}:`, error);
    // 如果出错，尝试使用默认语言
    const defaultLocale = 'zh';
    const defaultMessages = await getMessages({ locale: defaultLocale });
    
    return (
      <NextIntlClientProvider locale={defaultLocale} messages={defaultMessages}>
        {children}
      </NextIntlClientProvider>
    );
  }
} 