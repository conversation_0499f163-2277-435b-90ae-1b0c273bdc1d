import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// 支持的语言列表
export const locales = ['zh', 'en', 'ko'] as const;
export type Locale = (typeof locales)[number];
export const defaultLocale: Locale = 'zh';

export default getRequestConfig(async ({ locale }) => {
  // 验证语言是否支持
  if (!locales.includes(locale as Locale)) {
    notFound();
  }

  try {
    // 动态加载对应语言的消息
    const messages = (await import(`../../../../messages/${locale}.json`)).default;
    
    return {
      locale: locale as string,
      messages,
    };
  } catch (error) {
    console.error(`加载语言文件失败: ${locale}`, error);
    // 如果加载失败，尝试使用默认语言
    const defaultMessages = (await import(`../../../../messages/${defaultLocale}.json`)).default;
    return {
      locale: locale as string,
      messages: defaultMessages,
    };
  }
}); 