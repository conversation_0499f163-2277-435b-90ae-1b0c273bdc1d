{"name": "game-payment-portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-tooltip": "^1.2.6", "@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@types/crypto-js": "^4.2.2", "@types/md5": "^2.3.5", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "framer-motion": "^12.10.5", "gsap": "^3.13.0", "lucide-react": "^0.509.0", "md5": "^2.3.0", "next": "15.3.2", "next-intl": "^4.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^3.2.0", "three": "^0.176.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}