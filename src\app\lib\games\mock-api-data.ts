import { ApiGame } from './types';

// 模拟的API返回数据（包含多语言支持）
export const API_GAMES_DATA: Record<string, ApiGame> = {
  "pubg-mobile": {
    id: "pubg-mobile",
    bannerUrl: "/images/games/1.png",
    rating: 4.8,
    playerCount: "5000万+",
    i18n: {
      title: {
        zh: "绝地求生：刺激战场",
        en: "PUBG Mobile",
        ko: "배틀그라운드 모바일"
      },
      description: {
        zh: "《绝地求生：刺激战场》是一款由腾讯游戏开发的多人在线生存射击游戏，玩家需要在一个不断缩小的地图上与其他玩家战斗，成为最后的幸存者。",
        en: "PUBG Mobile is a multiplayer online battle royale game developed by Tencent Games, where players fight against each other on a continuously shrinking map to be the last one standing.",
        ko: "배틀그라운드 모바일은 텐센트 게임즈에서 개발한 멀티플레이어 온라인 배틀로얄 게임으로, 플레이어는 지속적으로 축소되는 맵에서 서로 싸워 마지막까지 살아남는 사람이 되어야 합니다."
      },
      category: {
        zh: "射击",
        en: "Shooter",
        ko: "슈팅"
      }
    },
    products: [
      {
        id: "pubg-uc-60",
        imageUrl: "/images/games/1.png",
        price: 6,
        i18n: {
          title: {
            zh: "60 UC",
            en: "60 UC",
            ko: "60 UC"
          },
          description: {
            zh: "可用于购买游戏内道具和服装",
            en: "Can be used to purchase in-game items and outfits",
            ko: "게임 내 아이템 및 의상 구매에 사용 가능"
          }
        }
      },
      {
        id: "pubg-uc-300",
        imageUrl: "/images/games/1.png",
        price: 30,
        originalPrice: 36,
        i18n: {
          title: {
            zh: "300 UC",
            en: "300 UC",
            ko: "300 UC"
          },
          description: {
            zh: "可用于购买游戏内道具和服装",
            en: "Can be used to purchase in-game items and outfits",
            ko: "게임 내 아이템 및 의상 구매에 사용 가능"
          }
        }
      },
      {
        id: "pubg-uc-600",
        imageUrl: "/images/products/pubg-uc.png",
        price: 60,
        originalPrice: 72,
        i18n: {
          title: {
            zh: "600 UC",
            en: "600 UC",
            ko: "600 UC"
          },
          description: {
            zh: "可用于购买游戏内道具和服装",
            en: "Can be used to purchase in-game items and outfits",
            ko: "게임 내 아이템 및 의상 구매에 사용 가능"
          }
        }
      },
      {
        id: "pubg-uc-1500",
        imageUrl: "/images/products/pubg-uc.png",
        price: 150,
        originalPrice: 180,
        i18n: {
          title: {
            zh: "1500 UC",
            en: "1500 UC",
            ko: "1500 UC"
          },
          description: {
            zh: "可用于购买游戏内道具和服装",
            en: "Can be used to purchase in-game items and outfits",
            ko: "게임 내 아이템 및 의상 구매에 사용 가능"
          }
        }
      },
      {
        id: "pubg-uc-3000",
        imageUrl: "/images/products/pubg-uc.png",
        price: 300,
        originalPrice: 360,
        i18n: {
          title: {
            zh: "3000 UC",
            en: "3000 UC",
            ko: "3000 UC"
          },
          description: {
            zh: "可用于购买游戏内道具和服装",
            en: "Can be used to purchase in-game items and outfits",
            ko: "게임 내 아이템 및 의상 구매에 사용 가능"
          }
        }
      },
      {
        id: "pubg-royale-pass",
        imageUrl: "/images/products/pubg-royale-pass.png",
        price: 98,
        i18n: {
          title: {
            zh: "精英皇家通行证",
            en: "Elite Royale Pass",
            ko: "엘리트 로얄 패스"
          },
          description: {
            zh: "解锁额外的游戏内奖励和挑战",
            en: "Unlock additional in-game rewards and challenges",
            ko: "추가 게임 내 보상 및 도전 과제 잠금 해제"
          }
        }
      }
    ]
  },
  "honor-of-kings": {
    id: "honor-of-kings",
    imageUrl: "/images/games/honor-of-kings.jpg",
    bannerUrl: "/images/banners/honor-of-kings-banner.jpg",
    rating: 4.7,
    playerCount: "1亿+",
    i18n: {
      title: {
        zh: "王者荣耀",
        en: "Honor of Kings",
        ko: "왕자영요"
      },
      description: {
        zh: "《王者荣耀》是一款由腾讯游戏开发的多人在线战术竞技游戏，玩家需要选择不同的英雄角色，与队友合作击败敌方队伍。",
        en: "Honor of Kings is a multiplayer online battle arena game developed by Tencent Games, where players select different hero characters and work with teammates to defeat the opposing team.",
        ko: "왕자영요는 텐센트 게임즈에서 개발한 멀티플레이어 온라인 배틀 아레나 게임으로, 플레이어는 다양한 영웅 캐릭터를 선택하고 팀원과 협력하여 상대 팀을 물리쳐야 합니다."
      },
      category: {
        zh: "MOBA",
        en: "MOBA",
        ko: "MOBA"
      }
    },
    products: [
      {
        id: "hok-diamond-60",
        imageUrl: "/images/products/hok-diamond.png",
        price: 6,
        i18n: {
          title: {
            zh: "60 钻石",
            en: "60 Diamonds",
            ko: "60 다이아몬드"
          },
          description: {
            zh: "可用于购买游戏内英雄和皮肤",
            en: "Can be used to purchase in-game heroes and skins",
            ko: "게임 내 영웅 및 스킨 구매에 사용 가능"
          }
        }
      },
      {
        id: "hok-diamond-300",
        imageUrl: "/images/products/hok-diamond.png",
        price: 30,
        originalPrice: 36,
        i18n: {
          title: {
            zh: "300 钻石",
            en: "300 Diamonds",
            ko: "300 다이아몬드"
          },
          description: {
            zh: "可用于购买游戏内英雄和皮肤",
            en: "Can be used to purchase in-game heroes and skins",
            ko: "게임 내 영웅 및 스킨 구매에 사용 가능"
          }
        }
      },
      {
        id: "hok-diamond-600",
        imageUrl: "/images/products/hok-diamond.png",
        price: 60,
        originalPrice: 72,
        i18n: {
          title: {
            zh: "600 钻石",
            en: "600 Diamonds",
            ko: "600 다이아몬드"
          },
          description: {
            zh: "可用于购买游戏内英雄和皮肤",
            en: "Can be used to purchase in-game heroes and skins",
            ko: "게임 내 영웅 및 스킨 구매에 사용 가능"
          }
        }
      },
      {
        id: "hok-diamond-1500",
        imageUrl: "/images/products/hok-diamond.png",
        price: 150,
        originalPrice: 180,
        i18n: {
          title: {
            zh: "1500 钻石",
            en: "1500 Diamonds",
            ko: "1500 다이아몬드"
          },
          description: {
            zh: "可用于购买游戏内英雄和皮肤",
            en: "Can be used to purchase in-game heroes and skins",
            ko: "게임 내 영웅 및 스킨 구매에 사용 가능"
          }
        }
      },
      {
        id: "hok-diamond-3000",
        imageUrl: "/images/products/hok-diamond.png",
        price: 300,
        originalPrice: 360,
        i18n: {
          title: {
            zh: "3000 钻石",
            en: "3000 Diamonds",
            ko: "3000 다이아몬드"
          },
          description: {
            zh: "可用于购买游戏内英雄和皮肤",
            en: "Can be used to purchase in-game heroes and skins",
            ko: "게임 내 영웅 및 스킨 구매에 사용 가능"
          }
        }
      },
      {
        id: "hok-battle-pass",
        imageUrl: "/images/products/hok-battle-pass.png",
        price: 98,
        i18n: {
          title: {
            zh: "荣耀战令",
            en: "Glory Pass",
            ko: "영광의 패스"
          },
          description: {
            zh: "解锁额外的游戏内奖励和挑战",
            en: "Unlock additional in-game rewards and challenges",
            ko: "추가 게임 내 보상 및 도전 과제 잠금 해제"
          }
        }
      }
    ]
  }
}; 