import { getRequestConfig } from 'next-intl/server';

// 默认语言
const defaultLocale = 'zh';

// 预先导入所有语言文件
const messagesImports = {
  zh: () => import('../../../messages/zh.json'),
  en: () => import('../../../messages/en.json'),
  ko: () => import('../../../messages/ko.json'),
};

export default getRequestConfig(async ({ requestLocale }) => {
  // 获取请求的语言
  const locale = (await requestLocale) || defaultLocale;
  
  try {
    // 使用预定义的导入函数获取语言文件
    const importFn = messagesImports[locale as keyof typeof messagesImports] || messagesImports[defaultLocale];
    const messages = (await importFn()).default;
    
    return {
      locale,
      messages,
      // 可选：设置时区
      timeZone: 'Asia/Shanghai'
    };
  } catch (error) {
    console.error(`加载语言文件失败: ${locale}`, error);
    // 如果加载失败，尝试使用默认语言
    const defaultMessages = (await messagesImports[defaultLocale]()).default;
    return {
      locale,
      messages: defaultMessages,
      timeZone: 'Asia/Shanghai'
    };
  }
}); 